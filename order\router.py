"""
Order API Router.
Handles employment order management endpoints.
"""

from managers.logger_manager import logger
from fastapi import APIRouter, HTTPException
from typing import Dict, Any

from order.models import (
    EmploymentPickupExpiredRequest, EmploymentPickupExpiredResponse,
    EmploymentPickupRequest, EmploymentPickupResponse,
    EmploymentDeliverRequest, EmploymentDeliverResponse,
    OrderDeliverRequest, OrderDeliverResponse,
    EmploymentSendRequest, EmploymentSendResponse,
    CustomerPickupRequest, CustomerPickupResponse,
    CustomerReclaimRequest, CustomerReclaimResponse,
    CustomerSendRequest, CustomerSendResponse,
    CreateReservationRequest, CreateReservationResponse
)
from domains.order.service import order_service
from managers.timeline_logger import log_timeline_event
from infrastructure.repositories.order_repository import OrderRepository
from infrastructure.repositories.pin_generator import generate_pin

router = APIRouter()

@router.post("/operator/pickup-expired", response_model=EmploymentPickupExpiredResponse)
async def pickup_expired_orders(request: EmploymentPickupExpiredRequest):
    """
    Request to pickup expired orders.
    Creates a WebSocket session and starts pickup loop if expired orders are found.
    """
    try:
        result = await order_service.pickup_expired_orders(request.operator_id)
        
        # If we have sections to pickup, start the flow
        if result["success"] and result["total_sections"] > 0:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "pickup_expired"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "sections": result["sections"],
                    "operator_id": request.operator_id
                })
        
        return EmploymentPickupExpiredResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in pickup_expired_orders: {e}")
        log_timeline_event(
            event_type="pickup_expired",
            event_result="failed",
            operator_id=request.operator_id,
            message=f"Error in pickup_expired_orders: {str(e)}",
            mode="order"
        )
        return EmploymentPickupExpiredResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0
        )

@router.post("/operator/pickup", response_model=EmploymentPickupResponse)
async def pickup_employee_orders(request: EmploymentPickupRequest):
    """
    Function for courier to pickup orders from employees.
    Creates a WebSocket session and starts pickup loop if employee orders are found.
    """
    try:
        result = await order_service.pickup_employee_orders(request.operator_id)
        
        # If we have sections to pickup, start the flow
        if result["success"] and result["total_sections"] > 0:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "pickup"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "sections": result["sections"],
                    "operator_id": request.operator_id
                })
        
        return EmploymentPickupResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in pickup_employee_orders: {e}")
        log_timeline_event(
            event_type="pickup",
            event_result="failed",
            operator_id=request.operator_id,
            message=f"Error in pickup_employee_orders: {str(e)}",
            mode="order"
        )
        return EmploymentPickupResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            sections=[],
            total_sections=0
        )

@router.post("/employment/operator/deliver", response_model=EmploymentDeliverResponse)
async def deliver_to_employee(request: EmploymentDeliverRequest):
    """
    Function to deliver orders to employees. This function is for courier.
    Validates phone number and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.deliver_to_employee(request.phone_number)
        
        # If phone number is valid, start the flow
        if result["success"]:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "deliver_employee"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "phone_number": request.phone_number,
                    "reserved_section_ids": result.get("section_ids")
                })
        
        return EmploymentDeliverResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in deliver_to_employee: {e}")
        log_timeline_event(
            event_type="deliver_employee",
            event_result="failed",
            message=f"Error in deliver_to_employee: {str(e)}",
            mode="order"
        )
        return EmploymentDeliverResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            section_ids=None
        )

@router.post("/operator/deliver", response_model=OrderDeliverResponse)
async def deliver_order_by_number(request: OrderDeliverRequest):
    """
    Function to deliver orders using order number. This function is for courier.
    Validates order number and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.deliver_order_by_number(request.order_number)

        # If order number is valid, start the flow
        if result["success"]:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "deliver_order"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "order_number": request.order_number
                })

        return OrderDeliverResponse(**result)

    except Exception as e:
        logger.error(f"Error in deliver_order_by_number: {e}")
        log_timeline_event(
            event_type="deliver_order",
            event_result="failed",
            message=f"Error in deliver_order_by_number: {str(e)}",
            mode="order"
        )
        return OrderDeliverResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}"
        )

@router.post("/employment/customer/send", response_model=EmploymentSendResponse)
async def employee_send_order(request: EmploymentSendRequest):
    """
    Function for employee to send order.
    Validates phone number and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.employee_send_order(request.phone_number)
        
        # If phone number is valid, start the flow
        if result["success"] and result["valid"]:

            log_timeline_event(
                event_type="employee_send",
                event_result="started",
                phone_number=request.phone_number,
                message="Employee send order started",
                mode="order"
            )

            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "employee_send"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "phone_number": request.phone_number,
                    "reserved_section_id": result.get("section_id")
                })
        
        elif not result["valid"]:
            log_timeline_event(
                event_type="employee_send",
                event_result="phone_number_not_found",
                phone_number=request.phone_number,
                message="Invalid phone number",
                mode="order"
            )

        return EmploymentSendResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in employee_send_order: {e}")
        log_timeline_event(
            event_type="employee_send",
            event_result="failed",
            phone_number=request.phone_number,
            message=f"Error in employee_send_order: {str(e)}",
            mode="order"
        )
        return EmploymentSendResponse(
            session_id=None,
            success=False,
            section_id=None,
            valid=False,
            message=f"Internal server error: {str(e)}"
        )

@router.post("/customer/pickup", response_model=CustomerPickupResponse)
async def customer_pickup_order(request: CustomerPickupRequest):
    """
    Function for customer to pickup order using PIN.
    Similar to product pickup - creates WebSocket session and waits for hardware_screen_ready.
    """
    try:
        result = await order_service.customer_pickup_order(request.pickup_pin)
        
        # If order is found, start the flow
        if result["success"]:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "customer_pickup"
                session.section_id = result["section_id"]  # This field exists in SessionData
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "reservation_id": result.get("reservation_id")
                })

            log_timeline_event(
                event_type="pin_entered",
                event_result="order_found",
                entered_pin=request.pickup_pin,
                message="Customer pickup started",
                mode="order"
            )
        else:
            log_timeline_event(
                event_type="pin_entered",
                event_result="order_not_found",
                entered_pin=request.pickup_pin,
                message="Invalid pickup PIN",
                mode="order"
            )
        
        return CustomerPickupResponse(**result)
        
    except Exception as e:
        logger.error(f"Error in customer_pickup_order: {e}")
        log_timeline_event(
            event_type="pin_entered",
            event_result="failed",
            entered_pin=request.pickup_pin,
            message=f"Error in customer_pickup_order: {str(e)}",
            mode="order"
        )
        return CustomerPickupResponse(
            session_id=None,
            success=False,
            message=f"Internal server error: {str(e)}",
            section_id=None,
            requires_payment=False,
            amount=0.0
        )

@router.post("/employment/customer/reclaim", response_model=CustomerReclaimResponse)
async def customer_reclaim_order(request: CustomerReclaimRequest):
    """
    Function for customer to reclaim order using reclamation PIN.
    Validates reclamation PIN and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.customer_reclaim_order(request.reclamation_pin)

        # If reclamation PIN is valid, start the flow
        if result["success"] and result["valid"]:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "customer_reclaim"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "phone_number": result.get("phone_number"),
                    "reserved_section_id": result.get("section_id")
                })

            log_timeline_event(
                event_type="reclamation_pin_entered",
                event_result="valid_pin",
                entered_pin=request.reclamation_pin,
                message="Reclamation PIN is valid",
                mode="order"
            )
            
        else:
            log_timeline_event(
                event_type="pin_entered",
                event_result="invalid_pin",
                entered_pin=request.reclamation_pin,
                message="Invalid reclamation PIN",
                mode="order"
            )

        return CustomerReclaimResponse(**result)

    except Exception as e:
        logger.error(f"Error in customer_reclaim_order: {e}")
        log_timeline_event(
            event_type="reclamation_pin_entered",
            event_result="failed",
            entered_pin=request.reclamation_pin,
            message=f"Error in customer_reclaim_order: {str(e)}",
            mode="order"
        )
        return CustomerReclaimResponse(
            session_id=None,
            success=False,
            section_id=None,
            valid=False,
            message=f"Internal server error: {str(e)}"
        )

@router.post("/customer/send", response_model=CustomerSendResponse)
async def customer_send_order(request: CustomerSendRequest):
    """
    Function for customer to send order to courier using reservation PIN.
    Checks if reservation exists with status=8 (ready for insert) and creates WebSocket session for section selection.
    """
    try:
        result = await order_service.customer_send_order(request.reservation_pin)

        # If order is found, start the flow
        if result["success"]:
            # Update session with operation details for universal websocket handler
            from managers.session_manager import session_manager
            session = session_manager.get_session(result["session_id"])
            if session:
                session.operation = "customer_send"
                if not session.user_data:
                    session.user_data = {}
                session.user_data.update({
                    "reservation_pin": request.reservation_pin,
                    "reserved_section_id": result.get("section_id")
                })

            log_timeline_event(
                event_type="customer_send_started",
                event_result="success",
                entered_pin=str(request.reservation_pin),
                message="Customer send order started",
                mode="order"
            )

        return CustomerSendResponse(**result)

    except Exception as e:
        logger.error(f"Error in customer_send_order: {e}")
        log_timeline_event(
            event_type="customer_send",
            event_result="failed",
            message=f"Error in customer_send_order: {str(e)}",
            mode="order"
        )
        return CustomerSendResponse(
            session_id=None,
            success=False,
            section_id=None,
            message=f"Internal server error: {str(e)}"
        )


@router.post("/create_reservation", response_model=CreateReservationResponse)
async def create_reservation(request: CreateReservationRequest):
    """
    Create order reservations for selected sections.
    This endpoint is called after select_sections() completes successfully.

    Request body:
    {
        "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",
        "type": "succesfully_inserted", // or "failed_insert" if customer did not insert the order
        "sections": [1,2,3]
    }

    Response body:
    {
        "success": true,
        "message": "Created 3 reservations for 3 sections",
        "reservations_created": 3,
        "sections": [1,2,3]
        "reservation_uuids": ["1a219d3e-ed9b-411a-92c3-96c11b0bfda5", "1a219d3e-ed9b-411a-92c3-96c11b0bfda6", "1a219d3e-ed9b-411a-92c3-96c11b0bfda7"]
    }
    """
    try:
        if request.type == "succesfully_inserted":
            logger.info(f"Creating reservations for successfully inserted sections: {request.sections}")

            from managers.session_manager import session_manager
            from infrastructure.repositories.order_repository import OrderRepository

            # Get session data
            session = session_manager.get_session(request.session_id)
            if not session:
                return CreateReservationResponse(
                    success=False,
                    message="Session not found",
                    reservations_created=0,
                    sections=[]
                )

            # Get session details
            endpoint_type = getattr(session, 'endpoint_type', 'unknown')
            phone_number = getattr(session, 'phone_number', None)
            reservation_pin = getattr(session, 'reservation_pin', None)
            insert_pin = getattr(session, 'insert_pin', None)
            order_number = getattr(session, 'order_number', None) or (session.user_data.get('order_number') if session.user_data else None)

            repo = OrderRepository()
            reservations_created = 0
            created_sections = []
            reservation_uuids = []
            c_status = None
            c_pickup_pin = None
            c_phone_number = None

            # Create reservations based on endpoint type
            if endpoint_type == "order/employment/operator/deliver" and phone_number:
                c_status = 1
                c_pickup_pin = generate_pin()
                c_phone_number = phone_number

            elif endpoint_type == "order/operator/deliver" and order_number:
                # For order number delivery, use order_number as phone_number for reservation
                if getattr(session, "deliver_checked_from_server", False):
                    c_status = 1
                else:
                    c_status = 2
                c_pickup_pin = generate_pin()
                c_phone_number = order_number  # Use order_number as identifier

            elif endpoint_type == "order/employment/customer/send" and phone_number:
                c_status = 8
                c_phone_number = phone_number

            elif endpoint_type == "order/employment/customer/reclaim" and phone_number:
                c_status = 8
                c_phone_number = phone_number

            elif endpoint_type == "order/customer/send" and reservation_pin:
                c_status = 8
                # For customer send, phone_number should be available from session
                c_phone_number = phone_number

            else:
                logger.warning(f"Unknown endpoint_type '{endpoint_type}' or missing required data for reservation creation")

            # create reservation
            if c_status and c_phone_number:
                for section in request.sections:
                    result = repo.create_order_reservation(phone_number=c_phone_number, section_id=section, status=c_status, pickup_pin=c_pickup_pin, insert_pin=insert_pin)
                    if result.get("success"):
                        reservations_created += 1
                        created_sections.append(section)
                        reservation_uuids.append(result.get("uuid"))
                    else:
                        logger.error(f"Failed to create order reservation: {result.get('error')}")


            return CreateReservationResponse(
                success=reservations_created > 0,
                message=f"Created {reservations_created} reservations for {len(created_sections)} sections" if reservations_created > 0 else "No reservations created",
                reservations_created=reservations_created,
                sections=created_sections,
                reservation_uuids=reservation_uuids
            )
        
        elif request.type == "failed_insert":
            logger.info(f"Customer did not insert the order")
            
            log_timeline_event(
                event_type="insert_order",
                event_result="failed",
                message=f"Customer did not insert the order",
                mode="order"
            )
            
            return CreateReservationResponse(
                success=True,
                message=f"Customer did not insert the order",
                reservations_created=0,
                sections=[],
                reservation_uuids=[]
            )

    except Exception as e:
        logger.error(f"Error in create_reservation: {e}")
        return CreateReservationResponse(
            success=False,
            message=f"Internal server error: {str(e)}",
            reservations_created=0,
            sections=[],
            reservation_uuids=[]
        )
