"""
Product Service - Business logic for product operations.
Handles product pickup, insertion, and management operations.
"""

from typing import Dict, Optional, Any
from uuid import uuid4
from decimal import Decimal

from managers.session_manager import session_manager, SessionType, SessionStatus
from managers.timeline_logger import log_timeline_event
from managers.logger_manager import logger

class ProductService:
    """Service for product-related business operations"""
    
    def __init__(self):
        self.logger = logger
    
    async def pickup_product(
        self, 
        section_id: Optional[int], 
        reservation_pin: Optional[str]
    ) -> Dict[str, Any]:
        """
        Initialize product pickup process.
        Creates transaction session and starts flow.
        
        Args:
            section_id: Section ID to search in (customer pickup without PIN - only for non-reserved products)
            reservation_pin: Reservation PIN to search by (customer pickup with PIN - required for reserved products)
            
        Returns:
            Dict with pickup response data
            
        Note:
            - If product is reserved (reserved = 1), PIN is required
            - If product is not reserved, it can be picked up by section_id only
        """
        logger.info(f"Product pickup requested - section_id: {section_id}, reservation_pin: {reservation_pin}")
        
        try:
            # Import repository
            from infrastructure.repositories import product_repository
            
            # Find product for pickup using new universal function
            if reservation_pin:
                # Find by reservation_pin (customer pickup with PIN)
                products = product_repository.find_reservations(
                    reservation_pin=reservation_pin,
                    status=1,
                    reserved=1,
                    limit=1
                )
            elif section_id:
                # Find by section_id (customer pickup without PIN - only for non-reserved products)
                products = product_repository.find_reservations(
                    section_id=section_id,
                    status=1,
                    reserved=0,
                    limit=1,
                    order_by="created_at DESC"
                )
            else:
                products = []

            product = products[0] if products else None

            if not product:
                # Log timeline event for failed pickup attempt
                if reservation_pin:
                    log_timeline_event(
                        event_type="pin_entered",
                        event_result="order_not_found",
                        message="Product reserved Box not found",
                        entered_pin=reservation_pin,
                        mode="product"
                    )
                    raise ValueError(f"No reserved product found with PIN: {reservation_pin}")
                elif section_id:
                    # Check if there's a reserved product in this section
                    reserved_products = product_repository.find_reservations(
                        section_id=section_id,
                        status=1,
                        reserved=1,
                        limit=1,
                        order_by="created_at DESC"
                    )
                    if reserved_products:
                        log_timeline_event(
                            event_type="product_pickup",
                            event_result="reserved_product_requires_pin",
                            message=f"Product in section {section_id} is reserved and requires PIN",
                            section_id=section_id,
                            mode="product"
                        )
                        raise ValueError(f"Product in section {section_id} is reserved and requires PIN for pickup")
                    else:
                        log_timeline_event(
                            event_type="product_pickup",
                            event_result="product_not_found",
                            message=f"No available product found in section {section_id}",
                            section_id=section_id,
                            mode="product"
                        )
                        raise ValueError(f"No available product found in section {section_id}")
                else:
                    log_timeline_event(
                        event_type="pickup_attempt",
                        event_result="no_criteria_provided",
                        message="No product found for pickup with provided criteria",
                        mode="product"
                    )
                    raise ValueError("No product found for pickup with provided criteria")

            # Log successful product found
            if reservation_pin:
                log_timeline_event(
                    event_type="pin_entered",
                    event_result="section_found",
                    message="Product reserved Box found",
                    entered_pin=reservation_pin,
                    section_id=product['section_id'],
                    mode="product"
                )
            else:
                log_timeline_event(
                    event_type="product_pickup",
                    event_result="product_found",
                    message=f"Product found in section {section_id}",
                    section_id=section_id,
                    mode="product"
                )
            
            product_id = product['id']
            section_id = int(product['section_id'])
            product_uuid = product['uuid']
            price = float(product.get('price', 0))
            
            logger.info(f"Found product {product_id} for pickup")
            
            # Check if product requires payment
            requires_payment = price > 0
            logger.info(f"Product {product_id} requires payment: {requires_payment}")
            
            # Create session for product pickup using simple session type
            session_id = str(uuid4())

            session = session_manager.create_session(
                session_id=session_id,
                session_type=SessionType.PRODUCT_PICKUP,  # Simple session type like storage
                product_id=product_id,
                product_uuid=product_uuid,
                section_id=section_id,
                amount=price,
                operation="product_pickup",
                endpoint_type="product/pickup"
            )

            if not session:
                raise Exception("Failed to create session")

            return {
                "success": True,
                "session_id": session_id,
                "status": "flow_started",
                "requires_payment": requires_payment,
                "amount": price,
                "message": "Product pickup flow has been initiated, connect to WebSocket",
                "section_id": section_id
            }
                
        except ValueError as e:
            # Re-raise ValueError as is (for HTTP 404)
            raise
        except Exception as e:
            logger.error(f"Error in pickup service: {e}")
            raise Exception(f"Internal server error: {str(e)}")

    
    async def insert_custom_product(
        self, 
        section_id: int, 
        price: Decimal
    ) -> Dict[str, Any]:
        """
        Insert a custom product into database.
        
        Args:
            section_id: Section ID where product will be placed
            price: Product price
            
        Returns:
            Dict with insert result
        """
        from infrastructure.repositories import product_repository
        
        # Check if section already has active product using new universal function
        active_products = product_repository.find_reservations(
            section_id=section_id,
            limit=1
        )
        # Filter for active products (status != 0)
        has_active = any(r.get('status', 0) != 0 for r in active_products)
        if has_active:
            log_timeline_event(
                event_type="product_insert",
                event_result="section_occupied",
                message=f"Section {section_id} already has an active product",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": "Section already has an active product",
                "error_code": "100"
            }

        # Insert custom product using new universal function
        new_product = product_repository.create_reservation(
            section_id=section_id,
            price=float(price),
            type="custom",
        )
        if not new_product:
            log_timeline_event(
                event_type="product_insert",
                event_result="failed",
                message=f"Failed to insert custom product in section {section_id}",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": "Failed to insert custom product",
                "error_code": "103"
            }

        log_timeline_event(
            event_type="product_insert",
            event_result="success",
            message=f"Custom product inserted successfully in section {section_id} with price {price}",
            section_id=section_id,
            mode="product"
        )

        return {
            "success": True,
            "message": "Custom product inserted successfully",
            "product": new_product
        }
    
    async def insert_product_with_ean(
        self, 
        section_id: int, 
        ean: str
    ) -> Dict[str, Any]:
        """
        Insert a product with EAN code into database.
        
        Args:
            section_id: Section ID where product will be placed
            ean: EAN code of the product
            
        Returns:
            Dict with insert result
        """
        from infrastructure.repositories import product_repository
        from infrastructure.external_apis import jetveo_client
        
        # Check if section already has active product using new universal function
        active_products = product_repository.find_reservations(
            section_id=section_id,
            limit=1
        )
        # Filter for active products (status != 0)
        has_active = any(r.get('status', 0) != 0 for r in active_products)
        if has_active:
            log_timeline_event(
                event_type="product_insert",
                event_result="section_occupied",
                message=f"Section {section_id} already has an active product",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": "Section already has an active product",
                "error_code": "100"
            }

        # Fetch product information from external API
        external_product = await jetveo_client.fetch_product_by_ean(ean)
        if not external_product:
            log_timeline_event(
                event_type="product_insert",
                event_result="ean_not_found",
                message=f"Product with EAN {ean} not found in external API",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": f"Product with EAN {ean} not found in external API",
                "error_code": "111"
            }
        
        # Prepare product data for database
        product_data = {
            "name": external_product.text.cs.name,
            "description": external_product.text.cs.description,
            "price": external_product.price,
            "age_control_required": external_product.ageControl,
            "cover_image": external_product.coverImage
        }
        
        # Insert product into database using new universal function
        new_product = product_repository.create_reservation(
            section_id=section_id,
            price=product_data.get('price', 0),
            ean=ean,
            type="ean",
            name=product_data.get('name'),
            description=product_data.get('description'),
            age_control_required=product_data.get('age_control_required', False),
            cover_image=product_data.get('cover_image')
        )
        if not new_product:
            log_timeline_event(
                event_type="product_insert",
                event_result="failed",
                message=f"Failed to insert product with EAN {ean} into database",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": "Failed to insert product into database",
                "error_code": "103"
            }

        log_timeline_event(
            event_type="product_insert",
            event_result="success",
            message=f"Product with EAN {ean} inserted successfully in section {section_id}",
            section_id=section_id,
            mode="product"
        )

        return {
            "success": True,
            "message": "Product inserted successfully",
            "product": new_product,
            "external_product": external_product
        }
    
    async def remove_product(self, section_id: int) -> Dict[str, Any]:
        """
        Remove product from section.
        
        Args:
            section_id: Section ID to remove product from
            
        Returns:
            Dict with remove result
        """
        from infrastructure.repositories import product_repository
        
        # Remove product using new universal function
        result = await product_repository.edit_reservation(
            section_id=section_id,
            status_to_set=0,   # Set to inactive
            action=2  # Remove action
        )
        success = result is not None
        if not success:
            log_timeline_event(
                event_type="product_remove",
                event_result="failed",
                message=f"Failed to remove product from section {section_id}",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": "Failed to remove product",
                "error_code": "103"
            }

        log_timeline_event(
            event_type="product_remove",
            event_result="success",
            message=f"Products in section {section_id} have been deactivated",
            section_id=section_id,
            mode="product"
        )

        return {
            "success": True,
            "message": f"Products in section {section_id} have been deactivated"
        }
    
    async def remove_all_products(self) -> Dict[str, Any]:
        """
        Remove all products from all sections.
        
        Returns:
            Dict with remove result
        """
        from infrastructure.repositories import product_repository
        
        # Remove all products using new universal function
        # First get all sections with active products
        active_products = product_repository.find_reservations(status=1)
        sections_with_products = list(set(p['section_id'] for p in active_products))

        removed_count = 0
        for section_id in sections_with_products:
            try:
                result = await product_repository.edit_reservation(
                    section_id=int(section_id),
                    status_to_set=0,   # Set to inactive
                    action=2  # Remove action
                )
                if result:
                    removed_count += 1
            except Exception as e:
                self.logger.error(f"Error removing product from section {section_id}: {e}")
                continue

        success = removed_count > 0 or len(sections_with_products) == 0
        if not success:
            log_timeline_event(
                event_type="product_remove_all",
                event_result="failed",
                message="Failed to remove all products",
                mode="product"
            )
            return {
                "success": False,
                "message": "Failed to remove all products",
                "error_code": "103"
            }

        log_timeline_event(
            event_type="product_remove_all",
            event_result="success",
            message="All products have been deactivated",
            mode="product"
        )

        return {
            "success": True,
            "message": "All products have been deactivated"
        }
    
    async def list_products(self) -> Dict[str, Any]:
        """
        List all active products.
        
        Returns:
            Dict with products list
        """
        from infrastructure.repositories import product_repository
        
        # List products using new universal function with JOIN to box_sections
        conn = product_repository._get_db_connection()
        cursor = conn.cursor(dictionary=True)

        try:
            cursor.execute("""
                SELECT sr.*
                FROM sale_reservations sr
                INNER JOIN box_sections bs ON sr.section_id = bs.section_id
                WHERE sr.status != 0 AND bs.mode = 'sale'
                ORDER BY sr.created_at DESC
            """)
            products = cursor.fetchall()
        except Exception as err:
            self.logger.error(f"Database error listing products: {err}")
            products = []
        finally:
            cursor.close()
            conn.close()
        
        return {
            "success": True,
            "products": products,
            "total_count": len(products)
        }
    

    async def reserve_section(
        self, 
        section_id: int, 
        reservation_pin: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Reserve a section with optional PIN.
        
        Args:
            section_id: Section ID to reserve
            reservation_pin: Optional reservation PIN
            
        Returns:
            Dict with reservation result
        """
        from infrastructure.repositories import product_repository
        
        # Reserve section using new universal function
        # First, check if there's a product with status=1 in the section
        active_products = product_repository.find_reservations(
            section_id=section_id,
            status=1,
            limit=1
        )

        if not active_products:
            self.logger.error(f"No active product found in section {section_id}")
            result = None
        else:
            # Generate 6-digit PIN if not provided
            if not reservation_pin:
                from infrastructure.repositories.pin_generator import generate_pin
                reservation_pin = generate_pin()

                if reservation_pin is None:
                    self.logger.error("Failed to generate unique PIN for reservation")
                    result = None
                else:
                    self.logger.info(f"Generated PIN {reservation_pin} for section {section_id}")

                    # Use the new universal edit_reservation function
                    result = await product_repository.edit_reservation(
                        section_id=section_id,
                        reserved=1,
                        reservation_pin_new=reservation_pin,
                        action=1  # Action code 1 for reservation
                    )
            else:
                # Use provided PIN
                result = await product_repository.edit_reservation(
                    section_id=section_id,
                    reserved=1,
                    reservation_pin_new=reservation_pin,
                    action=1  # Action code 1 for reservation
                )

        if not result:
            log_timeline_event(
                event_type="section_reserve",
                event_result="failed",
                message=f"Failed to reserve section {section_id}",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "error_code": "103"
            }

        log_timeline_event(
            event_type="section_reserve",
            event_result="success",
            message=f"Section {section_id} reserved successfully",
            section_id=section_id,
            entered_pin=result.get("reservation_pin"),
            mode="product"
        )

        return {
            "success": True,
            "message": f"Section {section_id} reserved successfully",
            "reservation_pin": result.get("reservation_pin"),
            "product": result
        }
    
    # purchase_product method removed - no longer needed

# Remaining purchase_product method code removed
    
    async def get_hardware_config_for_section(self, section_id: int):
        """
        Get hardware configuration for a section from box_sections table.
        
        Args:
            section_id: The section ID to get configuration for
            
        Returns:
            SectionConfig object with hardware configuration or None if not found
        """
        from managers.session_manager import SectionConfig
        from infrastructure.repositories import product_repository
        
        # This should be moved to a separate hardware repository
        # For now, we'll keep it simple
        import mysql.connector
        from os import getenv
        
        conn = mysql.connector.connect(
            host=getenv("DB_HOST"),
            port=int(getenv("DB_PORT")),
            database=getenv("DB_NAME"),
            user=getenv("DB_USER"),
            password=getenv("DB_PASSWORD")
        )
        
        cursor = conn.cursor(dictionary=True)
        
        try:
            cursor.execute("""
                SELECT section_id, lock_id, tempered, led_section, visible
                FROM box_sections 
                WHERE section_id = %s AND visible = 1
            """, (section_id,))
            
            config = cursor.fetchone()
            
            if not config:
                return None
                
            return SectionConfig(
                section_id=config['section_id'],
                lock_id=int(config['lock_id']) if config['lock_id'] else section_id,
                is_tempered=bool(config['tempered']),
                led_section=config['led_section'] if config['led_section'] else None
            )
            
        except mysql.connector.Error as err:
            self.logger.error(f"Database error getting hardware config: {err}")
            return None
        finally:
            cursor.close()
            conn.close()

    async def update_product_price(
        self, 
        section_id: int, 
        new_price: Decimal
    ) -> Dict[str, Any]:
        """
        Update the price of a product in a specific section.
        
        Args:
            section_id: Section ID containing the product
            new_price: New price for the product
            
        Returns:
            Dict with update result
        """
        from infrastructure.repositories import product_repository
        
        # Validate price
        if new_price < 0:
            log_timeline_event(
                event_type="product_price_update",
                event_result="invalid_price",
                message=f"Attempted to set negative price {new_price} for section {section_id}",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": "Price cannot be negative",
                "error_code": "114"
            }

        # Update product price using new universal function
        # First, find the current product to get old price
        current_products = product_repository.find_reservations(
            section_id=section_id,
            status=1,
            limit=1
        )

        if not current_products:
            result = None
        else:
            old_price = current_products[0].get('price', 0)

            # Use the new universal edit_reservation function
            result = await product_repository.edit_reservation(
                section_id=section_id,
                price=float(new_price),
                action=3  # Action code 3 for price update (no status change)
            )

            if result:
                result['old_price'] = old_price  # Add old price to result
                self.logger.info(f"Successfully updated price in section {section_id} from {old_price} to {new_price}")

        if not result:
            log_timeline_event(
                event_type="product_price_update",
                event_result="product_not_found",
                message=f"No active product found in section {section_id}",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": f"No active product found in section {section_id}",
                "error_code": "115"
            }

        log_timeline_event(
            event_type="product_price_update",
            event_result="success",
            message=f"Product price updated from {result.get('old_price', 0)} to {new_price} in section {section_id}",
            section_id=section_id,
            mode="product"
        )

        return {
            "success": True,
            "message": f"Product price updated successfully in section {section_id}",
            "section_id": section_id,
            "old_price": float(result.get("old_price", 0)),
            "new_price": float(new_price),
            "product": result
        }

    async def cancel_reservation(self, section_id: int) -> Dict[str, Any]:
        """
        Cancel a reservation for a section.
        
        Args:
            section_id: Section ID to cancel reservation for
            
        Returns:
            Dict with cancellation result
        """
        from infrastructure.repositories import product_repository
        
        # Cancel reservation using new universal function
        # First, find the reserved product to get the PIN
        reserved_products = product_repository.find_reservations(
            section_id=section_id,
            status=1,
            reserved=1,
            limit=1
        )

        if not reserved_products:
            result = None
        else:
            cancelled_pin = reserved_products[0].get('reservation_pin')

            # Use the new universal edit_reservation function
            result = await product_repository.edit_reservation(
                section_id=section_id,
                reserved=0,
                reservation_pin_new=None,
                action=2  # Action code 2 for unreservation
            )

            if result:
                result['cancelled_pin'] = cancelled_pin  # Add cancelled PIN to result
                self.logger.info(f"Successfully cancelled reservation in section {section_id}, PIN: {cancelled_pin}")

        if not result:
            log_timeline_event(
                event_type="reservation_cancel",
                event_result="not_found",
                message=f"No reserved product found in section {section_id}",
                section_id=section_id,
                mode="product"
            )
            return {
                "success": False,
                "message": f"No reserved product found in section {section_id}",
                "error_code": "116"
            }

        log_timeline_event(
            event_type="reservation_cancel",
            event_result="success",
            message=f"Reservation cancelled successfully for section {section_id}",
            section_id=section_id,
            entered_pin=result.get("cancelled_pin"),
            mode="product"
        )

        return {
            "success": True,
            "message": f"Reservation cancelled successfully for section {section_id}",
            "section_id": section_id,
            "cancelled_pin": result.get("cancelled_pin"),
            "product": result
        }

# Global service instance
product_service = ProductService()
